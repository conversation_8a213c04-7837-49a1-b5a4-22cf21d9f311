#!/usr/bin/env python3
"""
Memory-efficient analysis of large QVD files
"""

from qvd import qvd_reader
import pandas as pd
import gc
import sys
import os

def analyze_large_qvd(filepath, sample_size=10000):
    """Analyze large QVD file with sampling approach"""
    print(f"\n{'='*60}")
    print(f"ANALYZING LARGE FILE: {filepath}")
    print(f"File size: {os.path.getsize(filepath) / (1024*1024*1024):.2f} GB")
    print(f"{'='*60}")
    
    try:
        print("Loading file structure...")
        df = qvd_reader.read(filepath)
        
        total_rows = len(df)
        total_cols = len(df.columns)
        
        print(f"Total rows: {total_rows:,}")
        print(f"Total columns: {total_cols}")
        
        print(f"\nCOLUMN NAMES:")
        print("-" * 40)
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. {col}")
        
        # Sample analysis to avoid memory issues
        if total_rows > sample_size:
            print(f"\nUsing sample of {sample_size:,} rows for detailed analysis...")
            # Take a random sample
            sample_df = df.sample(n=sample_size, random_state=42)
        else:
            sample_df = df
            
        print(f"\nDETAILED ANALYSIS (based on {len(sample_df):,} rows):")
        print("-" * 50)
        
        case_id_candidates = []
        
        for i, col in enumerate(df.columns):
            try:
                print(f"\nAnalyzing column {i+1}/{total_cols}: {col}")
                
                # Use sample for detailed analysis
                unique_count_sample = sample_df[col].nunique()
                total_sample = len(sample_df)
                non_null_sample = sample_df[col].count()
                
                # Estimate full dataset statistics
                estimated_unique = int(unique_count_sample * (total_rows / total_sample))
                completeness = non_null_sample / total_sample if total_sample > 0 else 0
                unique_ratio_sample = unique_count_sample / total_sample if total_sample > 0 else 0
                
                print(f"  Sample unique: {unique_count_sample:,} ({unique_ratio_sample:.1%})")
                print(f"  Estimated total unique: {estimated_unique:,}")
                print(f"  Completeness: {completeness:.1%}")
                
                # Get sample values
                sample_values = sample_df[col].dropna().head(3).astype(str).tolist()
                print(f"  Sample values: {sample_values}")
                
                # Case ID candidate evaluation
                if (completeness > 0.9 and 
                    unique_ratio_sample < 0.95 and 
                    unique_ratio_sample > 0.005 and  # Lower threshold for large files
                    unique_count_sample > 5):
                    
                    case_id_candidates.append({
                        'column': col,
                        'unique_count_sample': unique_count_sample,
                        'estimated_unique': estimated_unique,
                        'unique_ratio_sample': unique_ratio_sample,
                        'completeness': completeness,
                        'sample_values': sample_values
                    })
                    print(f"  ⭐ CASE ID CANDIDATE")
                
                # Force garbage collection every few columns
                if i % 5 == 0:
                    gc.collect()
                    
            except Exception as e:
                print(f"  Error analyzing column {col}: {e}")
        
        # Clean up main dataframe
        del df
        del sample_df
        gc.collect()
        
        # Display case ID candidates
        print(f"\n{'='*60}")
        print("CASE ID CANDIDATES SUMMARY")
        print(f"{'='*60}")
        
        if case_id_candidates:
            # Sort by completeness and reasonable uniqueness
            case_id_candidates.sort(
                key=lambda x: (x['completeness'], -abs(x['unique_ratio_sample'] - 0.05)), 
                reverse=True
            )
            
            print(f"Found {len(case_id_candidates)} potential case ID candidates:")
            print()
            
            for i, candidate in enumerate(case_id_candidates, 1):
                print(f"{i}. {candidate['column']}")
                print(f"   Sample unique: {candidate['unique_count_sample']:,} ({candidate['unique_ratio_sample']:.1%})")
                print(f"   Estimated total unique: {candidate['estimated_unique']:,}")
                print(f"   Completeness: {candidate['completeness']:.1%}")
                print(f"   Sample values: {candidate['sample_values']}")
                print()
        else:
            print("No clear case ID candidates found in the sample.")
        
        return case_id_candidates
        
    except Exception as e:
        print(f"Error analyzing {filepath}: {str(e)}")
        return []

def main():
    filepath = 'TR_Z01MM_ELS.qvd'
    
    if len(sys.argv) > 1:
        sample_size = int(sys.argv[1])
    else:
        sample_size = 10000
    
    print(f"Analyzing {filepath} with sample size {sample_size:,}")
    
    candidates = analyze_large_qvd(filepath, sample_size)
    
    print(f"\n{'='*60}")
    print("ANALYSIS COMPLETE")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
