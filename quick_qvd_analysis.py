#!/usr/bin/env python3
"""
Quick QVD analysis script for large files
"""

from qvd import qvd_reader
import pandas as pd
import sys
import gc

def analyze_qvd_structure(filepath):
    """Get basic structure info from QVD file"""
    print(f"\n{'='*50}")
    print(f"ANALYZING: {filepath}")
    print(f"{'='*50}")
    
    try:
        # Read the QVD file
        df = qvd_reader.read(filepath)
        
        print(f"Rows: {len(df):,}")
        print(f"Columns: {len(df.columns)}")
        
        print(f"\nCOLUMN ANALYSIS:")
        print("-" * 30)
        
        case_id_candidates = []
        
        for i, col in enumerate(df.columns):
            try:
                unique_count = df[col].nunique()
                total_count = len(df)
                non_null_count = df[col].count()
                completeness = non_null_count / total_count if total_count > 0 else 0
                unique_ratio = unique_count / total_count if total_count > 0 else 0
                
                print(f"{i+1:2d}. {col}")
                print(f"    Unique: {unique_count:,} ({unique_ratio:.1%})")
                print(f"    Complete: {completeness:.1%}")
                
                # Case ID candidate criteria
                if (completeness > 0.9 and 
                    unique_ratio < 0.95 and 
                    unique_ratio > 0.01 and 
                    unique_count > 1):
                    
                    case_id_candidates.append({
                        'column': col,
                        'unique_count': unique_count,
                        'unique_ratio': unique_ratio,
                        'completeness': completeness
                    })
                
                # Show sample values for promising candidates
                if unique_ratio < 0.5 and unique_count > 10:
                    sample_vals = df[col].dropna().head(3).astype(str).tolist()
                    print(f"    Samples: {sample_vals}")
                
                print()
                
            except Exception as e:
                print(f"    Error analyzing column {col}: {e}")
        
        # Sort and display case ID candidates
        if case_id_candidates:
            case_id_candidates.sort(key=lambda x: (x['completeness'], -abs(x['unique_ratio'] - 0.1)), reverse=True)
            
            print(f"CASE ID CANDIDATES:")
            print("-" * 30)
            for i, candidate in enumerate(case_id_candidates[:5], 1):
                print(f"{i}. {candidate['column']}")
                print(f"   Unique: {candidate['unique_count']:,} ({candidate['unique_ratio']:.1%})")
                print(f"   Complete: {candidate['completeness']:.1%}")
        else:
            print("No clear case ID candidates found.")
        
        # Clean up memory
        del df
        gc.collect()
        
        return case_id_candidates
        
    except Exception as e:
        print(f"Error reading {filepath}: {str(e)}")
        return []

def main():
    files = ['RH_VERLADUNG.qvd', 'TR_Z01MM_ELS.qvd', 'ZF_TRANSPORT.qvd']
    
    if len(sys.argv) > 1:
        # Analyze specific file
        filepath = sys.argv[1]
        analyze_qvd_structure(filepath)
    else:
        # Analyze all files
        all_candidates = {}
        for filepath in files:
            try:
                candidates = analyze_qvd_structure(filepath)
                all_candidates[filepath] = candidates
            except Exception as e:
                print(f"Failed to analyze {filepath}: {e}")
        
        # Summary
        print(f"\n{'='*50}")
        print("SUMMARY")
        print(f"{'='*50}")
        
        for filepath, candidates in all_candidates.items():
            print(f"\n{filepath}:")
            if candidates:
                print(f"  Best case ID: {candidates[0]['column']}")
            else:
                print("  No clear case ID found")

if __name__ == "__main__":
    main()
