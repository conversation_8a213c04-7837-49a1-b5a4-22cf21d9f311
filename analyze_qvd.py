#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze QVD files for process mining case ID identification
"""

import pandas as pd
from qvd import qvd_reader
import os
from collections import Counter

def analyze_qvd_file(filepath):
    """Analyze a single QVD file and return structure information"""
    print(f"\n{'='*60}")
    print(f"ANALYZING: {filepath}")
    print(f"{'='*60}")
    
    try:
        # Read the QVD file
        df = qvd_reader.read(filepath)
        
        print(f"File size: {os.path.getsize(filepath):,} bytes")
        print(f"Number of rows: {len(df):,}")
        print(f"Number of columns: {len(df.columns)}")
        
        print(f"\nCOLUMN INFORMATION:")
        print("-" * 40)
        
        column_info = []
        for col in df.columns:
            dtype = str(df[col].dtype)
            non_null_count = df[col].count()
            null_count = len(df) - non_null_count
            unique_count = df[col].nunique()
            
            # Sample values (first 3 non-null values)
            sample_values = df[col].dropna().head(3).astype(str).tolist()
            
            column_info.append({
                'column': col,
                'dtype': dtype,
                'non_null': non_null_count,
                'null_count': null_count,
                'unique_values': unique_count,
                'sample_values': sample_values
            })
            
            print(f"Column: {col}")
            print(f"  Data Type: {dtype}")
            print(f"  Non-null values: {non_null_count:,} ({non_null_count/len(df)*100:.1f}%)")
            print(f"  Null values: {null_count:,}")
            print(f"  Unique values: {unique_count:,}")
            print(f"  Sample values: {sample_values}")
            print()
        
        # Identify potential case ID candidates
        print(f"POTENTIAL CASE ID ANALYSIS:")
        print("-" * 40)
        
        case_id_candidates = []
        
        for info in column_info:
            col = info['column']
            unique_ratio = info['unique_values'] / len(df) if len(df) > 0 else 0
            completeness = info['non_null'] / len(df) if len(df) > 0 else 0
            
            # Criteria for case ID candidates:
            # 1. High completeness (>90% non-null)
            # 2. Not too unique (not 100% unique - that would be transaction ID)
            # 3. Not too few unique values (>1% of total records)
            # 4. String or numeric type
            
            is_candidate = (
                completeness > 0.9 and  # High completeness
                unique_ratio < 0.95 and  # Not completely unique
                unique_ratio > 0.01 and  # Not too few unique values
                info['unique_values'] > 1  # More than 1 unique value
            )
            
            if is_candidate:
                case_id_candidates.append({
                    'column': col,
                    'unique_ratio': unique_ratio,
                    'completeness': completeness,
                    'unique_count': info['unique_values'],
                    'dtype': info['dtype']
                })
        
        # Sort candidates by a scoring function
        case_id_candidates.sort(key=lambda x: (x['completeness'], -abs(x['unique_ratio'] - 0.1)), reverse=True)
        
        if case_id_candidates:
            print("Potential Case ID candidates (ranked):")
            for i, candidate in enumerate(case_id_candidates, 1):
                print(f"{i}. {candidate['column']}")
                print(f"   - Completeness: {candidate['completeness']:.1%}")
                print(f"   - Unique ratio: {candidate['unique_ratio']:.1%}")
                print(f"   - Unique values: {candidate['unique_count']:,}")
                print(f"   - Data type: {candidate['dtype']}")
        else:
            print("No clear case ID candidates found based on standard criteria.")
        
        # Show sample data (limited columns to avoid memory issues)
        print(f"\nSAMPLE DATA (first 3 rows, key columns only):")
        print("-" * 40)
        key_columns = df.columns[:min(5, len(df.columns))]  # Show first 5 columns only
        print(df[key_columns].head(3).to_string())
        
        return {
            'filepath': filepath,
            'row_count': len(df),
            'column_count': len(df.columns),
            'columns': column_info,
            'case_id_candidates': case_id_candidates,
            'sample_data': df.head()
        }
        
    except Exception as e:
        print(f"Error reading {filepath}: {str(e)}")
        return None

def main():
    """Main function to analyze all QVD files"""
    qvd_files = [
        'RH_VERLADUNG.qvd',
        'TR_Z01MM_ELS.qvd', 
        'ZF_TRANSPORT.qvd'
    ]
    
    results = []
    
    for qvd_file in qvd_files:
        if os.path.exists(qvd_file):
            result = analyze_qvd_file(qvd_file)
            if result:
                results.append(result)
        else:
            print(f"File not found: {qvd_file}")
    
    # Summary and recommendations
    print(f"\n{'='*60}")
    print("SUMMARY AND RECOMMENDATIONS")
    print(f"{'='*60}")
    
    for result in results:
        print(f"\nFile: {result['filepath']}")
        print(f"Rows: {result['row_count']:,}, Columns: {result['column_count']}")
        
        if result['case_id_candidates']:
            print(f"Top case ID candidate: {result['case_id_candidates'][0]['column']}")
        else:
            print("No clear case ID candidates identified")
    
    return results

if __name__ == "__main__":
    main()
